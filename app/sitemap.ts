import { MetadataRoute } from 'next'
import { createSupabaseServerClient } from '@/lib/supabase/client'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://onlydiary.app'
  const supabase = await createSupabaseServerClient()

  // Static pages
  const staticPages: MetadataRoute.Sitemap = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/trending`,
      lastModified: new Date(),
      changeFrequency: 'hourly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/terms`,
      lastModified: new Date(),
      changeFrequency: 'yearly',
      priority: 0.3,
    },
    {
      url: `${baseUrl}/privacy`,
      lastModified: new Date(),
      changeFrequency: 'yearly',
      priority: 0.3,
    },
    {
      url: `${baseUrl}/login`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/register`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.5,
    },
  ]

  try {
    // Get all public diary entries
    const { data: entries } = await supabase
      .from('diary_entries')
      .select('id, updated_at, user_id')
      .eq('is_hidden', false)
      .order('updated_at', { ascending: false })
      .limit(1000) // Limit for performance

    // Get all writers with public profiles
    const { data: writers } = await supabase
      .from('users')
      .select('id, custom_url, updated_at')
      .eq('role', 'writer')
      .order('updated_at', { ascending: false })
      .limit(500) // Limit for performance

    // Add diary entry pages
    const entryPages: MetadataRoute.Sitemap = entries?.map((entry) => ({
      url: `${baseUrl}/entry/${entry.id}`,
      lastModified: new Date(entry.updated_at),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    })) || []

    // Add writer profile pages
    const writerPages: MetadataRoute.Sitemap = writers?.map((writer) => ({
      url: writer.custom_url 
        ? `${baseUrl}/${writer.custom_url}`
        : `${baseUrl}/u/${writer.id}`,
      lastModified: new Date(writer.updated_at || new Date()),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    })) || []

    return [...staticPages, ...entryPages, ...writerPages]
  } catch (error) {
    console.error('Error generating sitemap:', error)
    return staticPages
  }
}
