import { createSupabaseServerClient } from "@/lib/supabase/client"
import Link from "next/link"
import Image from "next/image"
import { CleanBestsellerChart } from "@/components/CleanBestsellerChart"

interface TopDiaryEntry {
  entry_id: string
  title: string
  writer_name: string
  writer_id: string
  writer_avatar: string | null
  hourly_reactions: number
  total_reactions: number
  first_photo_url: string | null
  video_url: string | null
  video_title: string | null
  created_at: string
  is_free: boolean
}

function formatTimeAgo(dateString: string) {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
  
  if (diffInHours < 1) return "Just now"
  if (diffInHours === 1) return "1 hour ago"
  if (diffInHours < 24) return `${diffInHours} hours ago`
  
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays === 1) return "1 day ago"
  return `${diffInDays} days ago`
}

export default async function TrendingPage() {
  const supabase = await createSupabaseServerClient()

  // Get recent entries with videos
  const { data: recentEntries } = await supabase
    .from('diary_entries')
    .select(`
      id,
      title,
      created_at,
      is_free,
      users!inner (
        id,
        name,
        profile_picture_url
      ),
      photos (
        url
      ),
      videos (
        r2_public_url,
        title
      )
    `)
    .eq('is_hidden', false)
    .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
    .order('created_at', { ascending: false })
    .limit(50) // Get more entries to calculate reactions for

  if (!recentEntries || recentEntries.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-6">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              🔥 Trending Diary Entries
            </h1>
            <p className="text-gray-600">
              No entries from the past week
            </p>
          </div>
        </div>
      </div>
    )
  }

  // Get reaction counts for these entries
  const entryIds = recentEntries.map(e => e.id)
  const { data: reactionCounts } = await supabase
    .from('reactions')
    .select('diary_entry_id, created_at')
    .in('diary_entry_id', entryIds)

  // Get hourly reaction counts (reactions in the last hour)
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString()
  const { data: hourlyReactionCounts } = await supabase
    .from('reactions')
    .select('diary_entry_id')
    .in('diary_entry_id', entryIds)
    .gte('created_at', oneHourAgo)

  // Count total reactions per entry
  const totalReactionsMap = reactionCounts?.reduce((acc, reaction) => {
    acc[reaction.diary_entry_id] = (acc[reaction.diary_entry_id] || 0) + 1
    return acc
  }, {} as Record<string, number>) || {}

  // Count hourly reactions per entry
  const hourlyReactionsMap = hourlyReactionCounts?.reduce((acc, reaction) => {
    acc[reaction.diary_entry_id] = (acc[reaction.diary_entry_id] || 0) + 1
    return acc
  }, {} as Record<string, number>) || {}

  // Map entries with reaction counts and sort by total reactions
  const entries: TopDiaryEntry[] = recentEntries
    .map((entry: any) => ({
      entry_id: entry.id,
      title: entry.title,
      writer_name: entry.users?.[0]?.name || entry.users?.name,
      writer_id: entry.users?.[0]?.id || entry.users?.id,
      writer_avatar: entry.users?.[0]?.profile_picture_url || entry.users?.profile_picture_url,
      hourly_reactions: hourlyReactionsMap[entry.id] || 0,
      total_reactions: totalReactionsMap[entry.id] || 0,
      first_photo_url: entry.photos?.[0]?.url || null,
      video_url: entry.videos?.[0]?.r2_public_url || null,
      video_title: entry.videos?.[0]?.title || null,
      created_at: entry.created_at,
      is_free: entry.is_free
    }))
    .sort((a, b) => b.total_reactions - a.total_reactions) // Sort by total reactions descending
    .slice(0, 20) // Take top 20

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🔥 Trending Diary Entries
          </h1>
          <p className="text-gray-600">
            The most reacted diary entries from the past week
          </p>
          <p className="text-sm text-gray-500 mt-1 italic">
            Rankings update automatically every hour
          </p>

          {/* Navigation to other trending pages */}
          <div className="mt-4 flex justify-center gap-4">
            <Link
              href="/trending/audio"
              className="text-purple-600 hover:text-purple-700 font-medium text-sm"
            >
              🎙️ Trending Audio Posts
            </Link>
          </div>

          {/* Call to Action for Supporting Creators */}
          <div className="mt-6">
            <Link
              href="/discover"
              className="inline-flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              <span>💝</span>
              Explore Creators to Support
            </Link>
            <p className="text-xs text-gray-500 mt-2">
              Discover amazing writers and support their work
            </p>
          </div>
        </div>

        {/* Top Entries Grid */}
        {entries.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <p className="text-gray-500">No trending entries right now</p>
            <p className="text-sm text-gray-400 mt-2">
              Check back later or be the first to create a trending post!
            </p>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {entries.map((entry, index) => (
              <div key={entry.entry_id} className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                {/* Ranking Badge */}
                <div className="relative">
                  <div className={`absolute top-3 left-3 z-10 w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold text-white ${
                    index === 0 ? 'bg-yellow-500' :
                    index === 1 ? 'bg-gray-400' :
                    index === 2 ? 'bg-orange-500' :
                    'bg-gray-600'
                  }`}>
                    #{index + 1}
                  </div>

                  {/* Video, Photo or Placeholder */}
                  {entry.video_url ? (
                    <div className="relative w-full h-48 bg-black">
                      <video
                        className="w-full h-full object-cover"
                        preload="metadata"
                        muted
                      >
                        <source src={entry.video_url} type="video/mp4" />
                      </video>
                      <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                        <div className="w-12 h-12 bg-white/90 rounded-full flex items-center justify-center">
                          <svg className="w-6 h-6 text-gray-800 ml-1" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"/>
                          </svg>
                        </div>
                      </div>
                      <div className="absolute top-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
                        🎥 VIDEO
                      </div>
                    </div>
                  ) : entry.first_photo_url ? (
                    <Image
                      src={entry.first_photo_url}
                      alt={entry.title}
                      width={400}
                      height={192}
                      className="w-full h-48 object-cover"
                    />
                  ) : (
                    <div className="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                      <span className="text-4xl text-gray-400">📝</span>
                    </div>
                  )}
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="font-bold text-gray-900 mb-2 line-clamp-2">
                    {entry.title}
                  </h3>

                  {/* Writer Info */}
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
                      {entry.writer_avatar ? (
                        <Image
                          src={entry.writer_avatar}
                          alt={entry.writer_name}
                          width={32}
                          height={32}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-300 flex items-center justify-center text-xs text-gray-600">
                          {entry.writer_name.charAt(0).toUpperCase()}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {entry.writer_name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatTimeAgo(entry.created_at)}
                      </p>
                    </div>

                    {entry.is_free && (
                      <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                        FREE
                      </span>
                    )}
                  </div>

                  {/* Stats */}
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <span>🔥 {entry.hourly_reactions} this hour</span>
                    <span>{entry.total_reactions} total reactions</span>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    <Link
                      href={`/d/${entry.entry_id}`}
                      className="flex-1 bg-gray-800 text-white text-center py-2 px-4 rounded-lg font-medium hover:bg-gray-700 transition-colors"
                    >
                      Read Entry
                    </Link>
                    <Link
                      href={`/u/${entry.writer_id}`}
                      className="bg-gray-100 text-gray-700 py-2 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors"
                    >
                      Subscribe
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Trending Books by Reactions Section */}
        <div className="mt-16">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              💖 Most Loved Books
            </h2>
            <p className="text-gray-600">
              Books with the most reactions this week
            </p>
          </div>

          <TrendingBooksGrid />
        </div>

        {/* Bestselling Books Section */}
        <div className="mt-16">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              📚 Bestselling Books
            </h2>
            <p className="text-gray-600">
              The most purchased books this week
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-sm">
            <CleanBestsellerChart
              showHeader={true}
              limit={10}
            />

            <div className="text-center mt-6">
              <Link href="/bestsellers">
                <button className="bg-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-purple-700 transition-colors">
                  View All Bestsellers →
                </button>
              </Link>
            </div>
          </div>
        </div>

      </div>
    </div>
  )
}

// Trending Books Grid Component
async function TrendingBooksGrid() {
  const supabase = await createSupabaseServerClient()

  // Get books from the last week
  const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()

  const { data: books } = await supabase
    .from('projects')
    .select(`
      id,
      title,
      description,
      cover_image_url,
      genre,
      price_amount,
      sales_count,
      created_at,
      author_name,
      slug,
      user:users!user_id (
        id,
        name,
        profile_picture_url
      )
    `)
    .eq('is_ebook', true)
    .eq('is_complete', true)
    .eq('is_private', false)
    .gte('created_at', oneWeekAgo)
    .order('created_at', { ascending: false })
    .limit(50)

  if (!books || books.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-8 text-center">
        <p className="text-gray-500">No trending books right now</p>
        <p className="text-sm text-gray-400 mt-2">
          Check back later or be the first to create a trending book!
        </p>
      </div>
    )
  }

  const bookIds = books.map(book => book.id)

  // Get reaction counts for these books
  const { data: bookReactions } = await supabase
    .from('reactions')
    .select('book_id')
    .in('book_id', bookIds)
    .gte('created_at', oneWeekAgo)

  // Count reactions per book
  const bookReactionCounts = bookReactions?.reduce((acc, reaction) => {
    acc[reaction.book_id] = (acc[reaction.book_id] || 0) + 1
    return acc
  }, {} as Record<string, number>) || {}

  // Also get diary entries and audio posts for comprehensive trending
  const { data: diaryEntries } = await supabase
    .from('diary_entries')
    .select(`
      id,
      title,
      created_at,
      user:users!user_id (
        id,
        name,
        profile_picture_url
      )
    `)
    .eq('is_hidden', false)
    .gte('created_at', oneWeekAgo)
    .order('created_at', { ascending: false })
    .limit(50)

  const { data: audioPosts } = await supabase
    .from('audio_posts')
    .select(`
      id,
      description,
      created_at,
      user:users!user_id (
        id,
        name,
        profile_picture_url
      )
    `)
    .gte('created_at', oneWeekAgo)
    .order('created_at', { ascending: false })
    .limit(50)

  // Get reactions for diary entries and audio posts
  const diaryIds = diaryEntries?.map(entry => entry.id) || []
  const audioIds = audioPosts?.map(post => post.id) || []

  const { data: diaryReactions } = await supabase
    .from('reactions')
    .select('diary_entry_id')
    .in('diary_entry_id', diaryIds)
    .gte('created_at', oneWeekAgo)

  const { data: audioReactions } = await supabase
    .from('reactions')
    .select('audio_post_id')
    .in('audio_post_id', audioIds)
    .gte('created_at', oneWeekAgo)

  const diaryReactionCounts = diaryReactions?.reduce((acc, reaction) => {
    acc[reaction.diary_entry_id] = (acc[reaction.diary_entry_id] || 0) + 1
    return acc
  }, {} as Record<string, number>) || {}

  const audioReactionCounts = audioReactions?.reduce((acc, reaction) => {
    acc[reaction.audio_post_id] = (acc[reaction.audio_post_id] || 0) + 1
    return acc
  }, {} as Record<string, number>) || {}

  // Sort books by reaction count and take top 12
  const trendingBooks = books
    .map(book => ({
      ...book,
      reaction_count: bookReactionCounts[book.id] || 0
    }))
    .sort((a, b) => b.reaction_count - a.reaction_count)
    .slice(0, 12)

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
      {trendingBooks.map((book, index) => (
        <Link
          key={book.id}
          href={`/books/${book.slug || book.id}`}
          className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow group"
        >
          {/* Ranking Badge */}
          <div className="relative">
            <div className={`absolute top-3 left-3 z-10 w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold text-white ${
              index === 0 ? 'bg-yellow-500' :
              index === 1 ? 'bg-gray-400' :
              index === 2 ? 'bg-amber-600' :
              'bg-blue-500'
            }`}>
              {index + 1}
            </div>

            {/* Book Cover */}
            <div className="aspect-[3/4] bg-gradient-to-br from-blue-100 to-purple-100">
              {book.cover_image_url ? (
                <Image
                  src={book.cover_image_url}
                  alt={book.title}
                  width={300}
                  height={400}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <span className="text-4xl opacity-50">📖</span>
                </div>
              )}
            </div>
          </div>

          {/* Book Info */}
          <div className="p-4">
            <h3 className="font-serif text-lg font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
              {book.title}
            </h3>
            <p className="text-sm text-gray-600 mb-2">by {book.author_name || book.user?.name}</p>

            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-green-600">
                {book.price_amount === 0 ? 'Free' : `$${(book.price_amount / 100).toFixed(2)}`}
              </span>
              {book.reaction_count > 0 && (
                <div className="flex items-center gap-1 text-red-500">
                  <span className="text-sm">❤️</span>
                  <span className="text-sm font-medium">{book.reaction_count}</span>
                </div>
              )}
            </div>
          </div>
        </Link>
      ))}
    </div>
  )
}
