'use client'

import { useState } from 'react'
import { AudioPost } from '@/components/AudioPost'

interface TrendingAudioPost {
  id: string
  audio_url: string
  description: string
  duration_seconds: number
  created_at: string
  user_id: string
  user_name: string
  user_avatar: string | null
  user_has_day1_badge: boolean
  user_signup_number: number | null
  total_reactions: number
  daily_reactions: number
  reply_count: number
  engagement_score: number
}

interface TrendingAudioClientProps {
  audioPosts: TrendingAudioPost[]
  currentUserId?: string
}

export function TrendingAudioClient({ audioPosts, currentUserId }: TrendingAudioClientProps) {
  const [posts, setPosts] = useState(audioPosts)

  const handleAudioLove = async (postId: string, isLoved: boolean) => {
    // Update the local state optimistically
    setPosts(prev => prev.map(post => {
      if (post.id === postId) {
        return {
          ...post,
          total_reactions: isLoved ? post.total_reactions + 1 : post.total_reactions - 1
        }
      }
      return post
    }))
  }

  const handleAudioReply = (postId: string) => {
    // TODO: Implement audio reply functionality
    console.log('Audio reply for post:', postId)
  }

  if (posts.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-8 text-center">
        <p className="text-gray-500">No trending audio posts right now</p>
        <p className="text-sm text-gray-400 mt-2">
          Check back later or be the first to create a trending audio post!
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {posts.map((post, index) => {
        // Transform the trending post data to match AudioPost component expectations
        const audioPostData = {
          id: post.id,
          audio_url: post.audio_url,
          description: post.description,
          duration_seconds: post.duration_seconds,
          created_at: post.created_at,
          user_id: post.user_id,
          love_count: post.total_reactions,
          reply_count: post.reply_count,
          user: {
            id: post.user_id,
            name: post.user_name,
            avatar: post.user_avatar,
            profile_picture_url: post.user_avatar,
            has_day1_badge: post.user_has_day1_badge,
            signup_number: post.user_signup_number
          },
          reactions: { fire: post.total_reactions }, // Set the actual reaction count
          userReaction: null,
          type: 'audio' as const,
          isFollowing: false, // We don't have this data in trending
          isSubscribed: false
        }

        return (
          <div key={post.id} className="relative">
            {/* Trending Header with Ranking */}
            <div className="flex items-center justify-between mb-3 px-4 py-2 bg-gradient-to-r from-purple-50 to-blue-50 rounded-t-lg border-b border-purple-100">
              <div className="flex items-center gap-3">
                {/* Ranking Badge */}
                <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold text-white shadow-lg ${
                  index === 0 ? 'bg-gradient-to-br from-yellow-400 to-yellow-600' :
                  index === 1 ? 'bg-gradient-to-br from-gray-300 to-gray-500' :
                  index === 2 ? 'bg-gradient-to-br from-orange-400 to-orange-600' :
                  'bg-gradient-to-br from-gray-400 to-gray-600'
                }`}>
                  #{index + 1}
                </div>

                <div className="text-sm">
                  <div className="font-semibold text-gray-800">
                    {index === 0 ? '🏆 Most Trending' :
                     index === 1 ? '🥈 Second Place' :
                     index === 2 ? '🥉 Third Place' :
                     `#${index + 1} Trending`}
                  </div>
                  <div className="text-xs text-gray-600">
                    🔥 {post.daily_reactions} reactions today • 💬 {post.reply_count} replies
                  </div>
                </div>
              </div>
            </div>

            {/* Audio Post Component */}
            <div className="bg-white rounded-b-lg shadow-sm">
              <AudioPost
                post={audioPostData}
                currentUserId={currentUserId}
                isFollowing={false}
                onLove={handleAudioLove}
                onReply={handleAudioReply}
                onUserClick={(userId) => {
                  window.location.href = `/u/${userId}`
                }}
              />
            </div>
          </div>
        )
      })}
    </div>
  )
}
