import { createSupabaseServerClient } from "@/lib/supabase/client"
import Link from "next/link"
import { TrendingAudioClient } from "./TrendingAudioClient"

interface TrendingAudioPost {
  id: string
  audio_url: string
  description: string
  duration_seconds: number
  created_at: string
  user_id: string
  user_name: string
  user_avatar: string | null
  total_reactions: number
  daily_reactions: number
  reply_count: number
  engagement_score: number
}

function formatTimeAgo(dateString: string) {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
  
  if (diffInHours < 1) return "Just now"
  if (diffInHours === 1) return "1 hour ago"
  if (diffInHours < 24) return `${diffInHours} hours ago`
  
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays === 1) return "1 day ago"
  return `${diffInDays} days ago`
}

function formatDuration(seconds: number) {
  return `${seconds}s`
}

export default async function TrendingAudioPage() {
  const supabase = await createSupabaseServerClient()

  // Get current user for reactions
  const { data: { user } } = await supabase.auth.getUser()

  // Get all audio posts (since the feature is new)
  const { data: recentAudioPosts, error: audioError } = await supabase
    .from('audio_posts')
    .select(`
      id,
      audio_url,
      description,
      duration_seconds,
      created_at,
      user_id,
      reply_count,
      user:users!user_id (
        id,
        name,
        profile_picture_url,
        has_day1_badge,
        signup_number
      )
    `)
    .order('created_at', { ascending: false })
    .limit(50)

  if (audioError) {
    console.error('Error fetching audio posts:', audioError)
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-6">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              🎙️ Trending Audio Posts
            </h1>
            <p className="text-gray-600">
              Error loading audio posts: {audioError.message}
            </p>
          </div>
        </div>
      </div>
    )
  }

  if (!recentAudioPosts || recentAudioPosts.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-6">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              🎙️ Trending Audio Posts
            </h1>
            <p className="text-gray-600">
              No audio posts found
            </p>
            <p className="text-sm text-gray-500 mt-2">
              Be the first to create an audio post!
            </p>
          </div>
        </div>
      </div>
    )
  }

  // Get reaction counts for these audio posts
  const audioIds = recentAudioPosts.map(post => post.id)
  const { data: reactionCounts } = await supabase
    .from('reactions')
    .select('audio_post_id, created_at')
    .in('audio_post_id', audioIds)

  // Get daily reaction counts (reactions in the last 24 hours)
  const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
  const { data: dailyReactionCounts } = await supabase
    .from('reactions')
    .select('audio_post_id')
    .in('audio_post_id', audioIds)
    .gte('created_at', oneDayAgo)

  // Count total reactions per audio post
  const totalReactionsMap = reactionCounts?.reduce((acc, reaction) => {
    acc[reaction.audio_post_id] = (acc[reaction.audio_post_id] || 0) + 1
    return acc
  }, {} as Record<string, number>) || {}

  // Count daily reactions per audio post
  const dailyReactionsMap = dailyReactionCounts?.reduce((acc, reaction) => {
    acc[reaction.audio_post_id] = (acc[reaction.audio_post_id] || 0) + 1
    return acc
  }, {} as Record<string, number>) || {}

  // Map audio posts with reaction counts and calculate engagement score
  const audioPosts: TrendingAudioPost[] = recentAudioPosts
    .map((post: any) => {
      const totalReactions = totalReactionsMap[post.id] || 0
      const dailyReactions = dailyReactionsMap[post.id] || 0
      const replyCount = post.reply_count || 0

      // Engagement score: weight recent activity more heavily
      const engagementScore = (totalReactions * 1) + (dailyReactions * 3) + (replyCount * 2)

      return {
        id: post.id,
        audio_url: post.audio_url,
        description: post.description,
        duration_seconds: post.duration_seconds,
        created_at: post.created_at,
        user_id: post.user_id,
        user_name: post.user?.name || 'Unknown User',
        user_avatar: post.user?.profile_picture_url || null,
        user_has_day1_badge: post.user?.has_day1_badge || false,
        user_signup_number: post.user?.signup_number || null,
        total_reactions: totalReactions,
        daily_reactions: dailyReactions,
        reply_count: replyCount,
        engagement_score: engagementScore
      }
    })
    .sort((a, b) => b.engagement_score - a.engagement_score) // Sort by engagement score
    .slice(0, 20) // Take top 20

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🎙️ Trending Audio Posts
          </h1>
          <p className="text-gray-600">
            The most engaging 9-second audio posts
          </p>
          <p className="text-sm text-gray-500 mt-1 italic">
            Ranked by total reactions, daily activity, and replies
          </p>

          {/* Navigation Links */}
          <div className="mt-6 flex justify-center gap-4">
            <Link
              href="/trending"
              className="text-blue-600 hover:text-blue-700 font-medium"
            >
              ← Back to All Trending
            </Link>
            <Link
              href="/discover"
              className="inline-flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              <span>💝</span>
              Explore Creators
            </Link>
          </div>
        </div>

        {/* Trending Audio Posts */}
        <TrendingAudioClient audioPosts={audioPosts} currentUserId={user?.id} />
      </div>
    </div>
  )
}
