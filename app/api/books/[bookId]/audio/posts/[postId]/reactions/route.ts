import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'
import { checkBookReadAccess } from '@/lib/utils/book-access'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ bookId: string; postId: string }> }
) {
  try {
    const { bookId, postId } = await params
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has access to this book
    const accessResult = await checkBookReadAccess(supabase, user.id, bookId)
    if (!accessResult.hasAccess) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    const { reactionType } = await request.json()

    if (!reactionType) {
      return NextResponse.json({ error: 'Reaction type is required' }, { status: 400 })
    }

    // Verify the post belongs to this book
    const { data: post, error: postError } = await supabase
      .from('book_audio_posts')
      .select('id')
      .eq('id', postId)
      .eq('project_id', bookId)
      .single()

    if (postError || !post) {
      return NextResponse.json({ error: 'Post not found' }, { status: 404 })
    }

    // Check if user already has a reaction on this post
    const { data: existingReaction } = await supabase
      .from('reactions')
      .select('id, reaction_type')
      .eq('user_id', user.id)
      .eq('book_audio_post_id', postId)
      .single()

    if (existingReaction) {
      if (existingReaction.reaction_type === reactionType) {
        // Remove reaction if it's the same
        const { error } = await supabase
          .from('reactions')
          .delete()
          .eq('user_id', user.id)
          .eq('book_audio_post_id', postId)

        if (error) {
          console.error('Error removing reaction:', error)
          return NextResponse.json({ error: 'Failed to remove reaction' }, { status: 500 })
        }

        return NextResponse.json({ reactionType: null })
      } else {
        // Update existing reaction
        const { error } = await supabase
          .from('reactions')
          .update({ reaction_type: reactionType })
          .eq('user_id', user.id)
          .eq('book_audio_post_id', postId)

        if (error) {
          console.error('Error updating reaction:', error)
          return NextResponse.json({ error: 'Failed to update reaction' }, { status: 500 })
        }

        return NextResponse.json({ reactionType })
      }
    } else {
      // Add new reaction
      const { error } = await supabase
        .from('reactions')
        .insert({
          user_id: user.id,
          book_audio_post_id: postId,
          reaction_type: reactionType
        })

      if (error) {
        console.error('Error adding reaction:', error)
        return NextResponse.json({ error: 'Failed to add reaction' }, { status: 500 })
      }

      return NextResponse.json({ reactionType })
    }
  } catch (error) {
    console.error('Book audio post reaction API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ bookId: string; postId: string }> }
) {
  try {
    const { bookId, postId } = await params
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()

    // Check if user has access to this book
    if (user) {
      const accessResult = await checkBookReadAccess(supabase, user.id, bookId)
      if (!accessResult.hasAccess) {
        return NextResponse.json({ error: 'Access denied' }, { status: 403 })
      }
    }

    // Get reaction counts for this post
    const { data: reactions, error: reactionsError } = await supabase
      .from('reactions')
      .select('reaction_type')
      .eq('book_audio_post_id', postId)

    if (reactionsError) {
      console.error('Error fetching reactions:', reactionsError)
      return NextResponse.json({ error: 'Failed to fetch reactions' }, { status: 500 })
    }

    // Count reactions by type
    const reactionCounts: Record<string, number> = {}
    reactions?.forEach(reaction => {
      reactionCounts[reaction.reaction_type] = (reactionCounts[reaction.reaction_type] || 0) + 1
    })

    // Get user's reaction if logged in
    let userReaction = null
    if (user) {
      const { data: userReactionData } = await supabase
        .from('reactions')
        .select('reaction_type')
        .eq('user_id', user.id)
        .eq('book_audio_post_id', postId)
        .single()

      userReaction = userReactionData?.reaction_type || null
    }

    return NextResponse.json({ 
      reactions: reactionCounts,
      userReaction 
    })
  } catch (error) {
    console.error('Book audio post reaction status API error:', error)
    return NextResponse.json({ 
      reactions: {},
      userReaction: null 
    })
  }
}
