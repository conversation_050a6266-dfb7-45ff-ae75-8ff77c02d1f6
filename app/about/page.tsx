import { NavigationButton } from "@/components/NavigationButton"

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">

      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 py-20 sm:py-32">
          <div className="text-center">
            <h1 className="text-4xl sm:text-6xl lg:text-7xl font-serif text-white mb-6">
              A New Kind of Social Network
            </h1>
            <p className="text-xl sm:text-2xl text-white/90 font-serif max-w-4xl mx-auto mb-8">
              Built on diary entries instead of posts. Write your truth. Share your story. Build readers, not followers.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <NavigationButton
                href="/register"
                variant="secondary"
                size="lg"
                className="!bg-white !text-purple-600 hover:!bg-gray-100 font-bold border-0"
              >
                Start Writing Today
              </NavigationButton>
              <NavigationButton
                href="/trending"
                variant="outline"
                size="lg"
                className="!border-white !text-white hover:!bg-white/10 font-bold"
              >
                Discover Stories
              </NavigationButton>
            </div>
          </div>
        </div>
      </div>

      {/* Revolutionary Features */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 py-20">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-5xl font-serif text-gray-800 mb-6">
            The Most Advanced Social Network Ever Built
          </h2>
          <p className="text-xl text-gray-600 font-serif max-w-4xl mx-auto">
            OnlyDiary represents a quantum leap in social technology. We&apos;ve engineered breakthrough features
            that don&apos;t exist anywhere else - from nested audio conversations to real-time book discussions,
            PWA technology, and revolutionary monetization systems that put creators first.
          </p>
        </div>

        {/* Core Philosophy */}
        <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-3xl p-8 sm:p-12 mb-20 text-center">
          <h3 className="text-2xl sm:text-3xl font-serif text-gray-800 mb-6">
            Built on a Revolutionary Philosophy
          </h3>
          <p className="text-lg text-gray-700 font-serif max-w-4xl mx-auto mb-8">
            &quot;People should show who they are authentically first (through diary entries) and THEN receive support,
            creating genuine connections.&quot; This isn&apos;t just marketing - it&apos;s the core architecture of how OnlyDiary works.
          </p>
          <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl mb-2">📖</div>
              <h4 className="font-semibold text-gray-800 mb-2">Diary Entries First</h4>
              <p className="text-gray-600 text-sm">Authentic storytelling before follower counts</p>
            </div>
            <div className="text-center">
              <div className="text-3xl mb-2">👥</div>
              <h4 className="font-semibold text-gray-800 mb-2">Readers, Not Followers</h4>
              <p className="text-gray-600 text-sm">Build meaningful connections through stories</p>
            </div>
            <div className="text-center">
              <div className="text-3xl mb-2">💝</div>
              <h4 className="font-semibold text-gray-800 mb-2">Support After Connection</h4>
              <p className="text-gray-600 text-sm">Monetization follows authentic relationships</p>
            </div>
          </div>
        </div>

        {/* Breakthrough Audio Technology */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-8 sm:p-12 mb-20 text-white">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-serif mb-4">
              🎵 OnlyAudio: The Future of Social Audio
            </h2>
            <p className="text-lg text-blue-100 font-serif max-w-4xl mx-auto">
              We&apos;ve invented an entirely new form of social interaction: nested audio conversations
              that create deeper connections than any platform has achieved before.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-xl">🎙️</span>
              </div>
              <h4 className="font-semibold mb-2">9-Second Audio Posts</h4>
              <p className="text-blue-100 text-sm">Perfect length for authentic moments with 50-character descriptions</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-xl">🌊</span>
              </div>
              <h4 className="font-semibold mb-2">Soundwave UI</h4>
              <p className="text-blue-100 text-sm">Visual waveforms that make audio posts beautiful and engaging</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-xl">🔥</span>
              </div>
              <h4 className="font-semibold mb-2">Nested Audio Replies</h4>
              <p className="text-blue-100 text-sm">Threaded audio conversations with fire emojis for deep discussions</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-xl">📚</span>
              </div>
              <h4 className="font-semibold mb-2">Book Audio Discussions</h4>
              <p className="text-blue-100 text-sm">Chapter-specific audio conversations integrated into e-reader</p>
            </div>
          </div>
        </div>

        {/* Revolutionary E-Reader Technology */}
        <div className="bg-gradient-to-r from-gray-900 to-gray-800 rounded-3xl p-8 sm:p-12 mb-20 text-white">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-serif mb-4">
              📖 Game-Changing E-Reader
            </h2>
            <p className="text-lg text-gray-300 font-serif max-w-4xl mx-auto">
              The world&apos;s most advanced web-based e-reader with features that don&apos;t exist anywhere else.
              We&apos;ve combined cutting-edge technology with social innovation.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-xl">✨</span>
              </div>
              <h4 className="font-semibold mb-2">Cinematic Animations</h4>
              <p className="text-gray-300 text-sm">Advanced 3D transitions and immersive reading experience</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-xl">💬</span>
              </div>
              <h4 className="font-semibold mb-2">Social Highlighting</h4>
              <p className="text-gray-300 text-sm">Highlight text and see what others found meaningful</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-xl">📝</span>
              </div>
              <h4 className="font-semibold mb-2">Margin Comments</h4>
              <p className="text-gray-300 text-sm">Leave thoughts in margins like traditional book annotations</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-xl">📱</span>
              </div>
              <h4 className="font-semibold mb-2">PWA Offline Reading</h4>
              <p className="text-gray-300 text-sm">Download books for offline reading with progress sync</p>
            </div>
          </div>
        </div>

        {/* Advanced Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">

          {/* Real-Time Everything */}
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-6">
              <span className="text-2xl">⚡</span>
            </div>
            <h3 className="text-xl font-serif text-gray-800 mb-4">Real-Time Platform</h3>
            <p className="text-gray-600 leading-relaxed">
              Comments appear instantly, replies update live, notifications deliver immediately.
              No refresh needed - experience the future of interactive writing with WebSocket technology.
            </p>
          </div>

          {/* Advanced Reaction System */}
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-6">
              <span className="text-2xl">😍</span>
            </div>
            <h3 className="text-xl font-serif text-gray-800 mb-4">Universal Reaction System</h3>
            <p className="text-gray-600 leading-relaxed">
              Facebook-style emoji reactions across all content types: diary entries, audio posts,
              book discussions, and nested replies. Hearts turn red when you love something.
            </p>
          </div>

          {/* Day 1 Badge Exclusivity */}
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6">
              <span className="text-2xl">🏆</span>
            </div>
            <h3 className="text-xl font-serif text-gray-800 mb-4">Day 1 Badge Exclusivity</h3>
            <p className="text-gray-600 leading-relaxed">
              Only the first 500 users get permanent Day 1 verification badges. These will become
              increasingly valuable as OnlyDiary grows into the premier creator platform.
            </p>
          </div>

          {/* Professional Book Publishing */}
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
            <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mb-6">
              <span className="text-2xl">📚</span>
            </div>
            <h3 className="text-xl font-serif text-gray-800 mb-4">Professional Publishing Suite</h3>
            <p className="text-gray-600 leading-relaxed">
              Intelligent chapter detection from document formatting, optimized book covers, automatic ebook export,
              multi-format support with advanced server-side processing and precision word counting.
            </p>
          </div>

          {/* Advanced Media Pipeline */}
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
            <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-6">
              <span className="text-2xl">🎬</span>
            </div>
            <h3 className="text-xl font-serif text-gray-800 mb-4">Advanced Media Pipeline</h3>
            <p className="text-gray-600 leading-relaxed">
              Photos with automatic watermarking, videos with custom thumbnails,
              enterprise-grade storage infrastructure, and intelligent media processing.
            </p>
          </div>

          {/* Dual Creator Economy */}
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
            <div className="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mb-6">
              <span className="text-2xl">👥</span>
            </div>
            <h3 className="text-xl font-serif text-gray-800 mb-4">Dual Creator Economy</h3>
            <p className="text-gray-600 leading-relaxed">
              Revolutionary follow vs subscribe system. Free following for discovery,
              paid subscriptions for intimate content. Build readers, not followers.
            </p>
          </div>

          {/* Smart Paywall Technology */}
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-6">
              <span className="text-2xl">🔒</span>
            </div>
            <h3 className="text-xl font-serif text-gray-800 mb-4">Smart Paywall System</h3>
            <p className="text-gray-600 leading-relaxed">
              Intelligent content truncation showing first sentence with word count previews.
              Seamless subscription flow with Stripe Connect integration.
            </p>
          </div>

          {/* PWA Technology */}
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
            <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mb-6">
              <span className="text-2xl">📱</span>
            </div>
            <h3 className="text-xl font-serif text-gray-800 mb-4">Progressive Web App</h3>
            <p className="text-gray-600 leading-relaxed">
              Download OnlyDiary as a native app. Offline reading, push notifications,
              microphone permissions, and app-like experience across all devices.
            </p>
          </div>

          {/* Advanced Monetization */}
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
            <div className="w-16 h-16 bg-violet-100 rounded-full flex items-center justify-center mb-6">
              <span className="text-2xl">💰</span>
            </div>
            <h3 className="text-xl font-serif text-gray-800 mb-4">Revolutionary Monetization</h3>
            <p className="text-gray-600 leading-relaxed">
              Multiple revenue streams: subscriptions (80% revenue), donations (95% revenue),
              book sales, waitlist notifications. Enterprise payment processing with instant withdrawals.
            </p>
          </div>
        </div>

        {/* Technical Innovation Showcase */}
        <div className="bg-gradient-to-r from-indigo-900 via-purple-900 to-pink-900 rounded-3xl p-8 sm:p-12 mb-20 text-white">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-serif mb-4">
              🚀 Next-Level Technical Innovation
            </h2>
            <p className="text-lg text-purple-100 font-serif max-w-4xl mx-auto">
              OnlyDiary runs on breakthrough technology that doesn&apos;t exist anywhere else.
              We&apos;ve engineered solutions that push the boundaries of what&apos;s possible on the web.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <div className="bg-white/10 rounded-2xl p-6 backdrop-blur-sm">
              <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mb-4">
                <span className="text-white text-xl">🎵</span>
              </div>
              <h4 className="font-semibold mb-3 text-lg">Advanced Audio Infrastructure</h4>
              <p className="text-purple-100 text-sm leading-relaxed">
                Proprietary audio processing pipeline with custom domain architecture.
                Intelligent recording limits and real-time waveform visualization technology.
              </p>
            </div>

            <div className="bg-white/10 rounded-2xl p-6 backdrop-blur-sm">
              <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mb-4">
                <span className="text-white text-xl">⚡</span>
              </div>
              <h4 className="font-semibold mb-3 text-lg">Breakthrough Real-Time Engine</h4>
              <p className="text-purple-100 text-sm leading-relaxed">
                Proprietary instant communication system with zero-latency interactions.
                Advanced connection resilience and automatic synchronization technology.
              </p>
            </div>

            <div className="bg-white/10 rounded-2xl p-6 backdrop-blur-sm">
              <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mb-4">
                <span className="text-white text-xl">📖</span>
              </div>
              <h4 className="font-semibold mb-3 text-lg">Revolutionary Reading Engine</h4>
              <p className="text-purple-100 text-sm leading-relaxed">
                Advanced 3D-accelerated e-reader with cinematic page transitions,
                social annotation layers, and integrated discussion technology.
              </p>
            </div>

            <div className="bg-white/10 rounded-2xl p-6 backdrop-blur-sm">
              <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center mb-4">
                <span className="text-white text-xl">🔄</span>
              </div>
              <h4 className="font-semibold mb-3 text-lg">Advanced Reaction System</h4>
              <p className="text-purple-100 text-sm leading-relaxed">
                Universal emoji reactions across diary entries, audio posts, book discussions,
                and nested replies with real-time count updates.
              </p>
            </div>

            <div className="bg-white/10 rounded-2xl p-6 backdrop-blur-sm">
              <div className="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center mb-4">
                <span className="text-white text-xl">📱</span>
              </div>
              <h4 className="font-semibold mb-3 text-lg">PWA with Offline Capabilities</h4>
              <p className="text-purple-100 text-sm leading-relaxed">
                Progressive Web App with offline reading, push notifications,
                microphone permissions, and native app experience.
              </p>
            </div>

            <div className="bg-white/10 rounded-2xl p-6 backdrop-blur-sm">
              <div className="w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center mb-4">
                <span className="text-white text-xl">🤖</span>
              </div>
              <h4 className="font-semibold mb-3 text-lg">Intelligent Document Processing</h4>
              <p className="text-purple-100 text-sm leading-relaxed">
                Advanced AI-powered chapter detection and hierarchical content analysis.
                Proprietary document parsing with precision word counting algorithms.
              </p>
            </div>
          </div>
        </div>

        {/* Creator Economy Innovation */}
        <div className="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-3xl p-8 sm:p-12 mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-serif text-gray-800 mb-4">
              💰 Revolutionary Creator Economy
            </h2>
            <p className="text-lg text-gray-700 font-serif max-w-4xl mx-auto">
              We&apos;ve built the most creator-friendly monetization system ever designed.
              Multiple revenue streams, transparent fees, and instant payouts.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
            <div className="bg-white rounded-2xl p-6 text-center shadow-sm">
              <div className="text-3xl font-bold text-emerald-600 mb-2">80%</div>
              <div className="text-lg font-serif text-gray-800 mb-2">Subscription Revenue</div>
              <div className="text-gray-600 text-sm">You keep $8 of every $10 subscription</div>
            </div>

            <div className="bg-white rounded-2xl p-6 text-center shadow-sm">
              <div className="text-3xl font-bold text-green-600 mb-2">95%</div>
              <div className="text-lg font-serif text-gray-800 mb-2">Donation Revenue</div>
              <div className="text-gray-600 text-sm">You keep $9.50 of every $10 donation</div>
            </div>

            <div className="bg-white rounded-2xl p-6 text-center shadow-sm">
              <div className="text-3xl font-bold text-blue-600 mb-2">5¢</div>
              <div className="text-lg font-serif text-gray-800 mb-2">Waitlist Notifications</div>
              <div className="text-gray-600 text-sm">Earn from building anticipation</div>
            </div>

            <div className="bg-white rounded-2xl p-6 text-center shadow-sm">
              <div className="text-3xl font-bold text-purple-600 mb-2">∞</div>
              <div className="text-lg font-serif text-gray-800 mb-2">Book Sales</div>
              <div className="text-gray-600 text-sm">Unlimited earning potential</div>
            </div>
          </div>
        </div>

        {/* Badge System - Digital Scarcity */}
        <div className="bg-gradient-to-r from-amber-50 via-yellow-50 to-orange-50 rounded-3xl p-8 sm:p-12 mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-serif text-gray-800 mb-4">
              🏆 The OnlyDiary Badge System
            </h2>
            <p className="text-lg text-gray-700 font-serif max-w-4xl mx-auto mb-8">
              Like Bitcoin, OnlyDiary badges are built on true digital scarcity. Only 10,000 badges will ever exist,
              creating permanent value that increases as the platform grows. These aren&apos;t just profile decorations -
              they&apos;re digital assets that prove your early belief in authentic storytelling.
            </p>
          </div>

          {/* Badge Tiers */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto mb-12">
            <div className="bg-gradient-to-br from-purple-100 to-purple-200 rounded-2xl p-6 text-center border-2 border-purple-300">
              <div className="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl font-bold">1</span>
              </div>
              <h4 className="font-bold text-purple-800 mb-2 text-lg">Day 1 Badges</h4>
              <div className="text-2xl font-bold text-purple-600 mb-2">1-500</div>
              <p className="text-purple-700 text-sm">
                The most exclusive. Only 500 will ever exist. These represent the ultimate early adopter status.
              </p>
            </div>

            <div className="bg-gradient-to-br from-blue-100 to-blue-200 rounded-2xl p-6 text-center border-2 border-blue-300">
              <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-xl font-bold">1K</span>
              </div>
              <h4 className="font-bold text-blue-800 mb-2 text-lg">Pioneer Badges</h4>
              <div className="text-2xl font-bold text-blue-600 mb-2">501-1,000</div>
              <p className="text-blue-700 text-sm">
                First 1,000 users. Still incredibly rare and valuable as platform adoption grows.
              </p>
            </div>

            <div className="bg-gradient-to-br from-green-100 to-green-200 rounded-2xl p-6 text-center border-2 border-green-300">
              <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-xl font-bold">5K</span>
              </div>
              <h4 className="font-bold text-green-800 mb-2 text-lg">Early Adopter</h4>
              <div className="text-2xl font-bold text-green-600 mb-2">1,001-5,000</div>
              <p className="text-green-700 text-sm">
                First 5,000 users. Significant early adopter recognition with lasting value.
              </p>
            </div>

            <div className="bg-gradient-to-br from-orange-100 to-orange-200 rounded-2xl p-6 text-center border-2 border-orange-300">
              <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-lg font-bold">10K</span>
              </div>
              <h4 className="font-bold text-orange-800 mb-2 text-lg">Founding Member</h4>
              <div className="text-2xl font-bold text-orange-600 mb-2">5,001-10,000</div>
              <p className="text-orange-700 text-sm">
                First 10,000 users. The final tier before badges become unavailable forever.
              </p>
            </div>
          </div>

          {/* Bitcoin-Style Scarcity Explanation */}
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200 mb-8">
            <h3 className="text-2xl font-serif text-gray-800 mb-6 text-center">
              💎 Digital Scarcity Like Bitcoin
            </h3>
            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h4 className="font-semibold text-gray-800 mb-3">🔒 Permanent Scarcity</h4>
                <p className="text-gray-600 text-sm mb-4">
                  Just like Bitcoin&apos;s 21 million coin limit, OnlyDiary will only ever have 10,000 badges.
                  Once they&apos;re gone, they&apos;re gone forever. No exceptions, no additional releases.
                </p>

                <h4 className="font-semibold text-gray-800 mb-3">📈 Increasing Value</h4>
                <p className="text-gray-600 text-sm">
                  As OnlyDiary grows from thousands to millions of users, badge holders will represent
                  an increasingly exclusive group. Early Bitcoin adopters saw massive value appreciation -
                  badge holders could experience similar dynamics.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-800 mb-3">🎯 Proof of Early Belief</h4>
                <p className="text-gray-600 text-sm mb-4">
                  Your badge proves you believed in authentic storytelling before it was mainstream.
                  This becomes more valuable as the platform validates the vision.
                </p>

                <h4 className="font-semibold text-gray-800 mb-3">🚀 Future Utility</h4>
                <p className="text-gray-600 text-sm">
                  Badge holders may receive exclusive features, priority access to new tools,
                  special creator programs, and other benefits as the platform evolves.
                </p>
              </div>
            </div>
          </div>

          {/* Value Proposition */}
          <div className="text-center">
            <h3 className="text-2xl font-serif text-gray-800 mb-4">
              Why Badges Will Become Valuable
            </h3>
            <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <div className="bg-white rounded-xl p-6 shadow-sm">
                <div className="text-3xl mb-3">🌟</div>
                <h4 className="font-semibold text-gray-800 mb-2">Social Proof</h4>
                <p className="text-gray-600 text-sm">
                  Instant credibility as an early believer in authentic storytelling
                </p>
              </div>
              <div className="bg-white rounded-xl p-6 shadow-sm">
                <div className="text-3xl mb-3">🎁</div>
                <h4 className="font-semibold text-gray-800 mb-2">Exclusive Access</h4>
                <p className="text-gray-600 text-sm">
                  Priority features, beta access, and special creator opportunities
                </p>
              </div>
              <div className="bg-white rounded-xl p-6 shadow-sm">
                <div className="text-3xl mb-3">💰</div>
                <h4 className="font-semibold text-gray-800 mb-2">Network Effects</h4>
                <p className="text-gray-600 text-sm">
                  As OnlyDiary grows, badge rarity increases exponentially in value
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Competitive Advantages */}
        <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-3xl p-8 sm:p-12 mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-serif text-gray-800 mb-4">
              🏆 Why OnlyDiary Dominates
            </h2>
            <p className="text-lg text-gray-700 font-serif max-w-4xl mx-auto">
              We&apos;ve built features that don&apos;t exist anywhere else. While other platforms focus on
              follower counts and viral content, we&apos;ve engineered authentic connection technology.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <div className="bg-white rounded-2xl p-6 shadow-sm">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-4">
                <span className="text-orange-600 text-xl">🎵</span>
              </div>
              <h4 className="font-semibold text-gray-800 mb-3">Nested Audio Conversations</h4>
              <p className="text-gray-600 text-sm">
                No other platform has threaded audio replies with fire emoji depth indicators.
                This creates deeper connections than any social network has achieved.
              </p>
            </div>

            <div className="bg-white rounded-2xl p-6 shadow-sm">
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
                <span className="text-red-600 text-xl">📖</span>
              </div>
              <h4 className="font-semibold text-gray-800 mb-3">E-Reader Audio Discussions</h4>
              <p className="text-gray-600 text-sm">
                Chapter-specific audio conversations integrated into the reading experience.
                Revolutionary book club technology that doesn&apos;t exist anywhere else.
              </p>
            </div>

            <div className="bg-white rounded-2xl p-6 shadow-sm">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                <span className="text-purple-600 text-xl">⚡</span>
              </div>
              <h4 className="font-semibold text-gray-800 mb-3">True Real-Time Platform</h4>
              <p className="text-gray-600 text-sm">
                Comments appear instantly without refresh. Most platforms fake real-time -
                we built genuine WebSocket architecture for instant interactions.
              </p>
            </div>

            <div className="bg-white rounded-2xl p-6 shadow-sm">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                <span className="text-blue-600 text-xl">🏆</span>
              </div>
              <h4 className="font-semibold text-gray-800 mb-3">Bitcoin-Style Badge Scarcity</h4>
              <p className="text-gray-600 text-sm">
                Only 10,000 badges will ever exist across all tiers. True digital scarcity
                that creates lasting value as the platform grows to millions of users.
              </p>
            </div>

            <div className="bg-white rounded-2xl p-6 shadow-sm">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <span className="text-green-600 text-xl">💰</span>
              </div>
              <h4 className="font-semibold text-gray-800 mb-3">Creator-First Economics</h4>
              <p className="text-gray-600 text-sm">
                95% donation revenue, 80% subscription revenue, instant withdrawals.
                We take the smallest cut in the industry because creators deserve more.
              </p>
            </div>

            <div className="bg-white rounded-2xl p-6 shadow-sm">
              <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mb-4">
                <span className="text-indigo-600 text-xl">📱</span>
              </div>
              <h4 className="font-semibold text-gray-800 mb-3">PWA Innovation</h4>
              <p className="text-gray-600 text-sm">
                Download as a native app with offline reading, push notifications,
                and microphone access. Most platforms are just websites - we&apos;re an app.
              </p>
            </div>
          </div>
        </div>

        {/* Success Stories */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-serif text-gray-800 mb-6">
            Where Greatness Begins
          </h2>
          <p className="text-lg text-gray-600 font-serif max-w-3xl mx-auto mb-12">
            Every great writer started with a single story. OnlyDiary provides the platform, 
            tools, and audience to turn your authentic experiences into financial success.
          </p>
          
          <div className="grid sm:grid-cols-4 gap-6 max-w-5xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">$0</div>
              <div className="text-gray-600 text-sm">Platform fees to start</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">∞</div>
              <div className="text-gray-600 text-sm">Earning potential</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">24/7</div>
              <div className="text-gray-600 text-sm">Instant withdrawals</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-red-600 mb-2">⚡</div>
              <div className="text-gray-600 text-sm">Real-time everything</div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="bg-gray-900 rounded-3xl p-8 sm:p-12 text-center text-white">
          <h2 className="text-3xl sm:text-4xl font-serif mb-6">
            Your Story is Worth Telling
          </h2>
          <p className="text-xl font-serif mb-8 opacity-90">
            Join the creators who are building authentic connections through diary entries.
            Show who you are first, then receive support. Build readers, not followers.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <NavigationButton
              href="/register"
              variant="secondary"
              size="lg"
              className="!bg-white !text-gray-900 hover:!bg-gray-100 font-bold border-0"
            >
              Start Writing for Free
            </NavigationButton>
            <NavigationButton
              href="/trending"
              variant="outline"
              size="lg"
              className="!border-white !text-white hover:!bg-white/10 font-bold"
            >
              Read Success Stories
            </NavigationButton>
          </div>
        </div>
      </div>
    </div>
  )
}
