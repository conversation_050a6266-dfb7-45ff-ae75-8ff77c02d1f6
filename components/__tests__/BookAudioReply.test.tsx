import { render, screen } from '@testing-library/react'
import { BookAudioReply } from '../BookAudioReply'

// Mock the AudioPlayer component
jest.mock('../AudioPlayer', () => {
  return function MockAudioPlayer({ audioUrl, duration }: { audioUrl: string; duration: number }) {
    return <div data-testid="audio-player">Audio: {audioUrl} ({duration}s)</div>
  }
})

// Mock the ReactionSystem component
jest.mock('../ReactionSystem', () => {
  return function MockReactionSystem({ contentType, contentId }: { contentType: string; contentId: string }) {
    return <div data-testid="reaction-system">{contentType}: {contentId}</div>
  }
})

// Mock the AudioRecorder component
jest.mock('../AudioRecorder', () => {
  return function MockAudioRecorder() {
    return <div data-testid="audio-recorder">Audio Recorder</div>
  }
})

// Mock the Day1Badge component
jest.mock('../Day1Badge', () => {
  return function MockDay1Badge() {
    return <div data-testid="day1-badge">Day 1</div>
  }
})

const mockReply = {
  id: 'reply-1',
  user_id: 'user-1',
  audio_url: 'https://example.com/audio.mp3',
  duration_seconds: 5.5,
  love_count: 3,
  created_at: '2024-01-01T12:00:00Z',
  reaction_counts: { fire: 2, love: 1 },
  userReaction: 'fire',
  user: {
    id: 'user-1',
    name: 'Test User',
    profile_picture_url: 'https://example.com/avatar.jpg',
    has_day1_badge: true,
    signup_number: 42
  }
}

describe('BookAudioReply', () => {
  it('renders basic reply correctly', () => {
    render(
      <BookAudioReply
        reply={mockReply}
        bookId="book-1"
        currentUserId="current-user"
        level={0}
      />
    )

    expect(screen.getByText('Test User')).toBeInTheDocument()
    expect(screen.getByTestId('audio-player')).toBeInTheDocument()
    expect(screen.getByTestId('reaction-system')).toBeInTheDocument()
    expect(screen.getByText('Reply')).toBeInTheDocument()
  })

  it('shows threading indicators for nested replies', () => {
    render(
      <BookAudioReply
        reply={mockReply}
        bookId="book-1"
        currentUserId="current-user"
        level={2}
      />
    )

    // Should show level indicator
    expect(screen.getByText('L2')).toBeInTheDocument()
    // Should show fire emojis for level 2
    expect(screen.getByText('🔥🔥')).toBeInTheDocument()
  })

  it('shows maximum fire emojis for deep nesting', () => {
    render(
      <BookAudioReply
        reply={mockReply}
        bookId="book-1"
        currentUserId="current-user"
        level={3}
      />
    )

    // Should show level indicator
    expect(screen.getByText('L3')).toBeInTheDocument()
    // Should show maximum fire emojis for level 3+
    expect(screen.getByText('🔥🔥🔥')).toBeInTheDocument()
  })

  it('hides reply button at max nesting level', () => {
    render(
      <BookAudioReply
        reply={mockReply}
        bookId="book-1"
        currentUserId="current-user"
        level={3}
        maxNestingLevel={3}
      />
    )

    // Reply button should not be visible at max nesting level
    expect(screen.queryByText('Reply')).not.toBeInTheDocument()
  })

  it('shows Day 1 badge for eligible users', () => {
    render(
      <BookAudioReply
        reply={mockReply}
        bookId="book-1"
        currentUserId="current-user"
        level={0}
      />
    )

    expect(screen.getByTestId('day1-badge')).toBeInTheDocument()
  })

  it('formats time correctly', () => {
    const recentReply = {
      ...mockReply,
      created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString() // 30 minutes ago
    }

    render(
      <BookAudioReply
        reply={recentReply}
        bookId="book-1"
        currentUserId="current-user"
        level={0}
      />
    )

    expect(screen.getByText('30m ago')).toBeInTheDocument()
  })
})
