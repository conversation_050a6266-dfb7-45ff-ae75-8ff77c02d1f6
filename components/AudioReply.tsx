'use client'

import { useState } from 'react'
import Image from 'next/image'
import { AudioPlayer } from './AudioPlayer'
import { AudioRecorder } from './AudioRecorder'
import { ReactionSystem } from './ReactionSystem'
import { Heart, MessageCircle, User } from 'lucide-react'
import { Day1Badge } from './Day1Badge'

interface AudioReply {
  id: string
  user_id: string
  audio_url: string
  duration_seconds: number
  love_count: number
  created_at: string
  reactions?: Record<string, number>
  userReaction?: string | null
  user?: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
    has_day1_badge?: boolean
    signup_number?: number
  }
}

interface AudioReplyProps {
  reply: AudioReply
  currentUserId?: string
  onLove?: (replyId: string, isLoved: boolean) => void
  onReply?: (replyId: string) => void
  onNestedReply?: (parentReplyId: string, audioBlob: Blob, duration: number) => Promise<void>
  level?: number // For nested replies
}

export function AudioReply({
  reply,
  currentUserId,
  onLove,
  onReply,
  onNestedReply,
  level = 0
}: AudioReplyProps) {
  const [reactions, setReactions] = useState(reply.reactions || { fire: reply.love_count })
  const [userReaction, setUserReaction] = useState(reply.userReaction)
  const [showReplyRecorder, setShowReplyRecorder] = useState(false)

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
      return diffInMinutes < 1 ? 'Just now' : `${diffInMinutes}m ago`
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}d ago`
    }
  }

  const handleReactionUpdate = (newReactions: Record<string, number>, newUserReaction: string | null) => {
    setReactions(newReactions)
    setUserReaction(newUserReaction)

    // Call the parent's onLove callback if it exists
    if (onLove) {
      const isLoved = newUserReaction === 'fire'
      onLove(reply.id, isLoved)
    }
  }

  const handleReply = () => {
    setShowReplyRecorder(true)
  }

  const handleAudioReplyComplete = async (audioBlob: Blob, duration: number) => {
    try {
      if (onNestedReply) {
        await onNestedReply(reply.id, audioBlob, duration)
        setShowReplyRecorder(false)
      } else {
        console.log('Nested audio reply recorded:', { duration, size: audioBlob.size })
        setShowReplyRecorder(false)
        alert('Nested audio replies coming soon!')
      }
    } catch (error) {
      console.error('Error posting nested audio reply:', error)
      alert('Failed to post audio reply. Please try again.')
    }
  }

  const maxNestingLevel = 4 // Limit nesting depth

  // Get background color based on nesting level
  const getBackgroundColor = (level: number) => {
    const colors = [
      'bg-gray-50',      // Level 0 - Original post
      'bg-blue-50',      // Level 1 - First reply
      'bg-purple-50',    // Level 2 - Reply to reply
      'bg-green-50',     // Level 3 - Third level
      'bg-orange-50'     // Level 4+ - Deep nesting with fire emoji
    ]
    return colors[Math.min(level, colors.length - 1)]
  }

  // Get border color based on nesting level
  const getBorderColor = (level: number) => {
    const colors = [
      'border-gray-100',   // Level 0
      'border-blue-200',   // Level 1
      'border-purple-200', // Level 2
      'border-green-200',  // Level 3
      'border-orange-200'  // Level 4+
    ]
    return colors[Math.min(level, colors.length - 1)]
  }

  const showFireEmoji = level >= 4

  return (
    <div className={`${level > 0 ? 'ml-4 sm:ml-8 mt-3' : 'mt-4'} ${level > 0 ? `border-l-2 ${getBorderColor(level)} pl-2 sm:pl-4` : ''}`}>
      <div className={`${getBackgroundColor(level)} rounded-lg p-3 sm:p-4 relative`}>
        {/* Fire emoji for deep nesting */}
        {showFireEmoji && (
          <div className="absolute top-2 right-2 text-lg animate-pulse">
            🔥
          </div>
        )}
        {/* User info */}
        <div className="flex items-center gap-2 sm:gap-3 mb-3">
          <div className="w-7 h-7 sm:w-8 sm:h-8 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden flex-shrink-0">
            {reply.user?.avatar || reply.user?.profile_picture_url ? (
              <Image
                src={(reply.user.avatar || reply.user.profile_picture_url) as string}
                alt={reply.user.name || 'User avatar'}
                width={32}
                height={32}
                className="w-full h-full object-cover"
              />
            ) : (
              <span className="text-xs font-serif text-gray-500">
                {reply.user?.name?.charAt(0).toUpperCase() || '?'}
              </span>
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-1 sm:gap-2">
              <span className="font-medium text-gray-900 text-xs sm:text-sm truncate">
                {reply.user?.name || 'Unknown User'}
              </span>
              {reply.user?.has_day1_badge && (
                <Day1Badge
                  signupNumber={reply.user.signup_number}
                  size="sm"
                  className="flex-shrink-0"
                />
              )}
              <span className="text-gray-400 text-xs">•</span>
              <span className="text-gray-500 text-xs">
                {formatDate(reply.created_at)}
              </span>
            </div>
          </div>
        </div>

        {/* Audio player */}
        <div className="mb-3">
          <AudioPlayer
            audioUrl={reply.audio_url}
            duration={reply.duration_seconds}
            className="w-full max-w-full sm:max-w-sm"
          />
        </div>

        {/* Actions */}
        <div className="flex items-center gap-2 sm:gap-4">
          {/* Reaction System */}
          <ReactionSystem
            contentType="audio_reply"
            contentId={reply.id}
            currentUserId={currentUserId}
            reactions={reactions}
            userReaction={userReaction}
            onReactionUpdate={handleReactionUpdate}
          />

          {level < maxNestingLevel && (
            <button
              onClick={handleReply}
              disabled={!currentUserId}
              className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 rounded-lg transition-all text-blue-600 hover:bg-blue-50 min-h-[44px] ${
                !currentUserId ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              <span className="text-xs sm:text-sm">🎤</span>
              <span className="text-xs sm:text-sm font-medium">Reply</span>
            </button>
          )}
        </div>

        {/* Nested reply recorder */}
        {showReplyRecorder && (
          <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-sm font-medium text-blue-900">🎤 Record Audio Reply</h4>
              <button
                onClick={() => setShowReplyRecorder(false)}
                className="text-blue-600 hover:text-blue-800 text-sm"
              >
                Cancel
              </button>
            </div>
            <AudioRecorder
              maxDuration={9}
              onRecordingComplete={handleAudioReplyComplete}
              onCancel={() => setShowReplyRecorder(false)}
            />
          </div>
        )}
      </div>
    </div>
  )
}
