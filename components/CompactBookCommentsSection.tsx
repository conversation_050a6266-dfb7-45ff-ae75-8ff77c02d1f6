"use client"

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { BookCommentsSection } from "./BookCommentsSection"

interface CompactBookCommentsSectionProps {
  bookId: string
  canComment: boolean
  userId?: string
  isOpen: boolean
  onToggle: () => void
  commentCount?: number
}

export function CompactBookCommentsSection({
  bookId,
  canComment,
  userId,
  isOpen,
  onToggle,
  commentCount = 0
}: CompactBookCommentsSectionProps) {
  return (
    <div className="border-t border-gray-100">
      {/* Comment Toggle Button */}
      <button
        onClick={onToggle}
        className="w-full px-4 py-2 text-left text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition-colors flex items-center justify-between"
      >
        <span className="flex items-center gap-2">
          💬 {commentCount > 0 ? `${commentCount} comments` : 'Comments'}
        </span>
        <span className="text-xs">
          {isOpen ? '▲' : '▼'}
        </span>
      </button>

      {/* Expanded Comments Section */}
      {isOpen && (
        <div className="px-4 pb-4 bg-gray-50">
          <BookCommentsSection
            bookId={bookId}
            canComment={canComment}
            userId={userId}
            maxDepth={3}
            isCompact={true}
          />
        </div>
      )}
    </div>
  )
}
