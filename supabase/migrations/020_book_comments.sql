-- Add book comments support
-- This migration adds the ability to comment on books in addition to diary entries

-- First, let's make the comments table more flexible to support both diary entries and books
-- Add book_id column to existing comments table
ALTER TABLE comments ADD COLUMN book_id UUID REFERENCES projects(id) ON DELETE CASCADE;

-- Add constraint to ensure either diary_entry_id OR book_id is set, but not both
ALTER TABLE comments ADD CONSTRAINT comments_content_type_check 
  CHECK (
    (diary_entry_id IS NOT NULL AND book_id IS NULL) OR 
    (diary_entry_id IS NULL AND book_id IS NOT NULL)
  );

-- Add index for book comments
CREATE INDEX idx_comments_book_id ON comments(book_id);

-- Update the comment notifications function to handle book comments
CREATE OR REPLACE FUNCTION notify_comment_created()
RETURNS TRIGGER AS $$
DECLARE
    content_author_id UUID;
    content_title TEXT;
    commenter_name TEXT;
    parent_comment_author_id UUID;
    parent_commenter_name TEXT;
    content_type TEXT;
BEGIN
    -- Determine content type and get author info
    IF NEW.diary_entry_id IS NOT NULL THEN
        -- Get the diary entry author and title
        SELECT de.user_id, de.title INTO content_author_id, content_title
        FROM diary_entries de 
        WHERE de.id = NEW.diary_entry_id;
        content_type := 'diary_entry';
    ELSIF NEW.book_id IS NOT NULL THEN
        -- Get the book author and title
        SELECT p.user_id, p.title INTO content_author_id, content_title
        FROM projects p 
        WHERE p.id = NEW.book_id;
        content_type := 'book';
    ELSE
        -- Invalid comment, skip notification
        RETURN NEW;
    END IF;
    
    -- Get commenter name
    SELECT name INTO commenter_name
    FROM users 
    WHERE id = NEW.user_id;
    
    -- Don't notify if user is commenting on their own content
    IF content_author_id = NEW.user_id THEN
        RETURN NEW;
    END IF;
    
    -- If this is a reply, handle reply notification
    IF NEW.parent_comment_id IS NOT NULL THEN
        -- Get the parent comment author
        SELECT c.user_id INTO parent_comment_author_id
        FROM comments c 
        WHERE c.id = NEW.parent_comment_id;
        
        -- Get parent commenter name
        SELECT name INTO parent_commenter_name
        FROM users 
        WHERE id = parent_comment_author_id;
        
        -- Don't notify if replying to own comment
        IF parent_comment_author_id != NEW.user_id THEN
            -- Insert reply notification
            INSERT INTO notifications (
                user_id,
                type,
                title,
                message,
                data,
                created_at
            ) VALUES (
                parent_comment_author_id,
                'comment_reply',
                'New reply to your comment',
                commenter_name || ' replied to your comment on "' || content_title || '"',
                jsonb_build_object(
                    'comment_id', NEW.id,
                    'parent_comment_id', NEW.parent_comment_id,
                    'commenter_id', NEW.user_id,
                    'commenter_name', commenter_name,
                    'content_id', COALESCE(NEW.diary_entry_id, NEW.book_id),
                    'content_title', content_title,
                    'content_type', content_type
                ),
                NOW()
            );
        END IF;
    END IF;
    
    -- Insert main comment notification (for content author)
    INSERT INTO notifications (
        user_id,
        type,
        title,
        message,
        data,
        created_at
    ) VALUES (
        content_author_id,
        'new_comment',
        'New comment on your ' || content_type,
        commenter_name || ' commented on "' || content_title || '"',
        jsonb_build_object(
            'comment_id', NEW.id,
            'commenter_id', NEW.user_id,
            'commenter_name', commenter_name,
            'content_id', COALESCE(NEW.diary_entry_id, NEW.book_id),
            'content_title', content_title,
            'content_type', content_type
        ),
        NOW()
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Update RLS policies for comments to include book comments
-- Drop existing policy
DROP POLICY IF EXISTS "Users can view comments on accessible content" ON comments;

-- Create new policy that handles both diary entries and books
CREATE POLICY "Users can view comments on accessible content" ON comments
FOR SELECT USING (
    -- For diary entry comments
    (diary_entry_id IS NOT NULL AND (
        -- Public diary entries
        EXISTS (
            SELECT 1 FROM diary_entries de 
            WHERE de.id = diary_entry_id 
            AND de.is_hidden = false 
            AND de.is_free = true
        )
        OR
        -- User has subscription to the writer
        EXISTS (
            SELECT 1 FROM diary_entries de
            JOIN subscriptions s ON s.writer_id = de.user_id
            WHERE de.id = diary_entry_id
            AND s.subscriber_id = auth.uid()
            AND s.active_until > NOW()
        )
        OR
        -- User is the author
        EXISTS (
            SELECT 1 FROM diary_entries de 
            WHERE de.id = diary_entry_id 
            AND de.user_id = auth.uid()
        )
    ))
    OR
    -- For book comments (books are generally public once published)
    (book_id IS NOT NULL AND (
        EXISTS (
            SELECT 1 FROM projects p 
            WHERE p.id = book_id 
            AND p.is_private = false
        )
        OR
        -- User is the author
        EXISTS (
            SELECT 1 FROM projects p 
            WHERE p.id = book_id 
            AND p.user_id = auth.uid()
        )
    ))
);

-- Update insert policy for comments
DROP POLICY IF EXISTS "Users can create comments on accessible content" ON comments;

CREATE POLICY "Users can create comments on accessible content" ON comments
FOR INSERT WITH CHECK (
    auth.uid() IS NOT NULL AND (
        -- For diary entry comments
        (diary_entry_id IS NOT NULL AND (
            -- Public diary entries
            EXISTS (
                SELECT 1 FROM diary_entries de 
                WHERE de.id = diary_entry_id 
                AND de.is_hidden = false 
                AND de.is_free = true
            )
            OR
            -- User has subscription to the writer
            EXISTS (
                SELECT 1 FROM diary_entries de
                JOIN subscriptions s ON s.writer_id = de.user_id
                WHERE de.id = diary_entry_id
                AND s.subscriber_id = auth.uid()
                AND s.active_until > NOW()
            )
            OR
            -- User is the author
            EXISTS (
                SELECT 1 FROM diary_entries de 
                WHERE de.id = diary_entry_id 
                AND de.user_id = auth.uid()
            )
        ))
        OR
        -- For book comments (anyone can comment on public books)
        (book_id IS NOT NULL AND (
            EXISTS (
                SELECT 1 FROM projects p 
                WHERE p.id = book_id 
                AND p.is_private = false
            )
        ))
    )
);

-- Add comment count columns to projects table for performance
ALTER TABLE projects ADD COLUMN comment_count INTEGER DEFAULT 0;

-- Create function to update comment counts
CREATE OR REPLACE FUNCTION update_comment_counts()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Increment comment count
        IF NEW.diary_entry_id IS NOT NULL THEN
            -- Update diary entry comment count (if we add this column later)
            -- For now, we'll calculate dynamically
            NULL;
        ELSIF NEW.book_id IS NOT NULL THEN
            UPDATE projects 
            SET comment_count = comment_count + 1 
            WHERE id = NEW.book_id;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- Decrement comment count
        IF OLD.diary_entry_id IS NOT NULL THEN
            -- Update diary entry comment count (if we add this column later)
            NULL;
        ELSIF OLD.book_id IS NOT NULL THEN
            UPDATE projects 
            SET comment_count = GREATEST(comment_count - 1, 0) 
            WHERE id = OLD.book_id;
        END IF;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for comment count updates
CREATE TRIGGER update_comment_counts_trigger
    AFTER INSERT OR DELETE ON comments
    FOR EACH ROW
    EXECUTE FUNCTION update_comment_counts();

-- Initialize comment counts for existing books
UPDATE projects 
SET comment_count = (
    SELECT COUNT(*) 
    FROM comments 
    WHERE comments.book_id = projects.id 
    AND comments.is_deleted = false
)
WHERE comment_count IS NULL OR comment_count = 0;
