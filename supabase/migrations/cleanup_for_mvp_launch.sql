-- MVP Launch Cleanup - Keep only <PERSON> and <PERSON>
-- This script preserves all content from these two users and removes everything else

-- First, let's identify the user IDs we want to keep
-- You'll need to update these UUIDs with the actual IDs from your database
-- Run this query first to get the correct IDs:
-- SELECT id, name, email FROM users WHERE name IN ('<PERSON>', '<PERSON>');

-- Replace these with actual UUIDs from your database
-- SET @david_id = 'your-david-uuid-here';
-- SET @mike_id = 'your-mike-uuid-here';

-- For now, using placeholder approach - update the WHERE clauses with actual names/emails

-- Delete all reactions not from our kept users
DELETE FROM reactions 
WHERE user_id NOT IN (
  SELECT id FROM users WHERE name IN ('<PERSON>', '<PERSON>')
);

-- Delete all loves not from our kept users
DELETE FROM loves 
WHERE user_id NOT IN (
  SELECT id FROM users WHERE name IN ('<PERSON>', '<PERSON>')
);

-- Delete all comments not from our kept users or not on kept users' content
DELETE FROM comments 
WHERE user_id NOT IN (
  SELECT id FROM users WHERE name IN ('<PERSON>', '<PERSON>')
)
OR diary_entry_id NOT IN (
  SELECT id FROM diary_entries WHERE user_id IN (
    SELECT id FROM users WHERE name IN ('<PERSON>', 'Mike Ike')
  )
);

-- Delete all audio posts not from our kept users
DELETE FROM audio_posts 
WHERE user_id NOT IN (
  SELECT id FROM users WHERE name IN ('David Weaver', 'Mike Ike')
);

-- Delete all audio replies not from our kept users
DELETE FROM audio_replies 
WHERE user_id NOT IN (
  SELECT id FROM users WHERE name IN ('David Weaver', 'Mike Ike')
);

-- Delete all book projects not from our kept users
DELETE FROM book_projects 
WHERE user_id NOT IN (
  SELECT id FROM users WHERE name IN ('David Weaver', 'Mike Ike')
);

-- Delete all book chapters not from our kept users
DELETE FROM book_chapters 
WHERE project_id NOT IN (
  SELECT id FROM book_projects WHERE user_id IN (
    SELECT id FROM users WHERE name IN ('David Weaver', 'Mike Ike')
  )
);

-- Delete all book audio posts not from our kept users
DELETE FROM book_audio_posts 
WHERE user_id NOT IN (
  SELECT id FROM users WHERE name IN ('David Weaver', 'Mike Ike')
);

-- Delete all book audio replies not from our kept users
DELETE FROM book_audio_replies 
WHERE user_id NOT IN (
  SELECT id FROM users WHERE name IN ('David Weaver', 'Mike Ike')
);

-- Delete all diary entries not from our kept users
DELETE FROM diary_entries 
WHERE user_id NOT IN (
  SELECT id FROM users WHERE name IN ('David Weaver', 'Mike Ike')
);

-- Delete all subscriptions not involving our kept users
DELETE FROM subscriptions 
WHERE subscriber_id NOT IN (
  SELECT id FROM users WHERE name IN ('David Weaver', 'Mike Ike')
)
AND writer_id NOT IN (
  SELECT id FROM users WHERE name IN ('David Weaver', 'Mike Ike')
);

-- Delete all follows not involving our kept users
DELETE FROM follows 
WHERE follower_id NOT IN (
  SELECT id FROM users WHERE name IN ('David Weaver', 'Mike Ike')
)
AND following_id NOT IN (
  SELECT id FROM users WHERE name IN ('David Weaver', 'Mike Ike')
);

-- Delete all donations not involving our kept users
DELETE FROM donations 
WHERE donor_id NOT IN (
  SELECT id FROM users WHERE name IN ('David Weaver', 'Mike Ike')
)
AND recipient_id NOT IN (
  SELECT id FROM users WHERE name IN ('David Weaver', 'Mike Ike')
);

-- Delete all withdrawals not from our kept users
DELETE FROM withdrawals 
WHERE user_id NOT IN (
  SELECT id FROM users WHERE name IN ('David Weaver', 'Mike Ike')
);

-- Finally, delete all users except our kept ones
DELETE FROM users 
WHERE name NOT IN ('David Weaver', 'Mike Ike');

-- Reset sequences if needed (optional)
-- This ensures clean ID numbering for new users
-- ALTER SEQUENCE users_id_seq RESTART WITH 3;

-- Update user signup numbers to be 1 and 2 for Day 1 badges
UPDATE users 
SET signup_number = 1, has_day1_badge = true 
WHERE name = 'David Weaver';

UPDATE users 
SET signup_number = 2, has_day1_badge = true 
WHERE name = 'Mike Ike';

-- Verify the cleanup
SELECT 'Users remaining:' as check_type, count(*) as count FROM users
UNION ALL
SELECT 'Diary entries remaining:', count(*) FROM diary_entries
UNION ALL
SELECT 'Book projects remaining:', count(*) FROM book_projects
UNION ALL
SELECT 'Audio posts remaining:', count(*) FROM audio_posts
UNION ALL
SELECT 'Comments remaining:', count(*) FROM comments;
