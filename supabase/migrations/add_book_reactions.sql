-- Add book reactions support to existing reactions table
-- This extends the reactions table to support book reactions

-- First, add book_id column to reactions table
ALTER TABLE reactions ADD COLUMN book_id UUID REFERENCES projects(id) ON DELETE CASCADE;

-- Update the unique constraint to include book reactions
-- Drop the old constraint first
ALTER TABLE reactions DROP CONSTRAINT IF EXISTS reactions_diary_entry_id_user_id_key;

-- Add new constraint that ensures one reaction per user per content item
ALTER TABLE reactions ADD CONSTRAINT reactions_user_content_unique 
CHECK (
  (diary_entry_id IS NOT NULL AND audio_post_id IS NULL AND book_id IS NULL) OR
  (diary_entry_id IS NULL AND audio_post_id IS NOT NULL AND book_id IS NULL) OR
  (diary_entry_id IS NULL AND audio_post_id IS NULL AND book_id IS NOT NULL)
);

-- Add unique constraints for each content type
ALTER TABLE reactions ADD CONSTRAINT reactions_diary_user_unique 
UNIQUE(diary_entry_id, user_id);

ALTER TABLE reactions ADD CONSTRAINT reactions_audio_user_unique 
UNIQUE(audio_post_id, user_id);

ALTER TABLE reactions ADD CONSTRAINT reactions_book_user_unique 
UNIQUE(book_id, user_id);

-- Add index for book reactions
CREATE INDEX idx_reactions_book_id ON reactions(book_id);

-- Add RLS policy for book reactions
CREATE POLICY "Anyone can view book reactions"
ON reactions
FOR SELECT 
USING (book_id IS NOT NULL);

CREATE POLICY "Users can insert book reactions"
ON reactions
FOR INSERT
WITH CHECK (book_id IS NOT NULL AND user_id = auth.uid());

CREATE POLICY "Users can update their book reactions"
ON reactions
FOR UPDATE
USING (book_id IS NOT NULL AND user_id = auth.uid());

CREATE POLICY "Users can delete their book reactions"
ON reactions
FOR DELETE
USING (book_id IS NOT NULL AND user_id = auth.uid());
