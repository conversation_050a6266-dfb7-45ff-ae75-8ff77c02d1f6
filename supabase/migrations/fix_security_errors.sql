-- Fix Supabase Security Linter Errors
-- This addresses the RLS and Security Definer issues

-- 1. Enable RLS on highlights table
ALTER TABLE public.highlights ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for highlights
CREATE POLICY "Users can view highlights on content they have access to" ON public.highlights
  FOR SELECT USING (
    -- Allow if the highlighted content is accessible to the user
    EXISTS (
      SELECT 1 FROM projects p
      WHERE p.id = highlights.project_id
      AND (
        p.is_private = false
        OR p.user_id = auth.uid()
        OR EXISTS (
          SELECT 1 FROM subscriptions s
          WHERE s.writer_id = p.user_id
          AND s.subscriber_id = auth.uid()
          AND s.active_until > NOW()
        )
      )
    )
  );

CREATE POLICY "Users can create highlights on accessible content" ON public.highlights
  FOR INSERT WITH CHECK (
    auth.uid() IS NOT NULL
    AND user_id = auth.uid()
    AND EXISTS (
      SELECT 1 FROM projects p
      WHERE p.id = highlights.project_id
      AND (
        p.is_private = false
        OR p.user_id = auth.uid()
        OR EXISTS (
          SELECT 1 FROM subscriptions s
          WHERE s.writer_id = p.user_id
          AND s.subscriber_id = auth.uid()
          AND s.active_until > NOW()
        )
      )
    )
  );

CREATE POLICY "Users can update their own highlights" ON public.highlights
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own highlights" ON public.highlights
  FOR DELETE USING (user_id = auth.uid());

-- 2. Enable RLS on margin_comments table
ALTER TABLE public.margin_comments ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for margin_comments
CREATE POLICY "Users can view margin comments on accessible content" ON public.margin_comments
  FOR SELECT USING (
    -- Allow if the commented content is accessible to the user
    EXISTS (
      SELECT 1 FROM projects p
      WHERE p.id = margin_comments.project_id
      AND (
        p.is_private = false
        OR p.user_id = auth.uid()
        OR EXISTS (
          SELECT 1 FROM subscriptions s
          WHERE s.writer_id = p.user_id
          AND s.subscriber_id = auth.uid()
          AND s.active_until > NOW()
        )
      )
    )
  );

CREATE POLICY "Users can create margin comments on accessible content" ON public.margin_comments
  FOR INSERT WITH CHECK (
    auth.uid() IS NOT NULL
    AND user_id = auth.uid()
    AND EXISTS (
      SELECT 1 FROM projects p
      WHERE p.id = margin_comments.project_id
      AND (
        p.is_private = false
        OR p.user_id = auth.uid()
        OR EXISTS (
          SELECT 1 FROM subscriptions s
          WHERE s.writer_id = p.user_id
          AND s.subscriber_id = auth.uid()
          AND s.active_until > NOW()
        )
      )
    )
  );

CREATE POLICY "Users can update their own margin comments" ON public.margin_comments
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own margin comments" ON public.margin_comments
  FOR DELETE USING (user_id = auth.uid());

-- 3. Enable RLS on passage_popularity table
ALTER TABLE public.passage_popularity ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for passage_popularity
CREATE POLICY "Anyone can view passage popularity" ON public.passage_popularity
  FOR SELECT USING (true);

CREATE POLICY "System can update passage popularity" ON public.passage_popularity
  FOR ALL USING (true);

-- 4. Fix the security definer view issue
-- Drop and recreate the user_conversations view without SECURITY DEFINER
DROP VIEW IF EXISTS public.user_conversations;

-- Recreate without SECURITY DEFINER (uses SECURITY INVOKER by default)
CREATE VIEW public.user_conversations AS
SELECT DISTINCT
  CASE 
    WHEN c.user_id < de.user_id THEN c.user_id 
    ELSE de.user_id 
  END as user1_id,
  CASE 
    WHEN c.user_id < de.user_id THEN de.user_id 
    ELSE c.user_id 
  END as user2_id,
  MAX(c.created_at) as last_message_at,
  COUNT(*) as message_count
FROM comments c
JOIN diary_entries de ON c.diary_entry_id = de.id
WHERE c.user_id != de.user_id  -- Only conversations between different users
GROUP BY 
  CASE 
    WHEN c.user_id < de.user_id THEN c.user_id 
    ELSE de.user_id 
  END,
  CASE 
    WHEN c.user_id < de.user_id THEN de.user_id 
    ELSE c.user_id 
  END;

-- Grant appropriate permissions
GRANT SELECT ON public.user_conversations TO authenticated;

-- Verify the fixes
SELECT 'Security fixes applied successfully' as status;
